directories:
  output: jaras
  buildResources: build
appId: com.schoolbell.app
productName: نظام الجرس المدرسي
files:
  - filter:
      - '**/*'
      - '!node_modules/**/*'
      - node_modules/lowdb/**/*
      - node_modules/xlsx/**/*
asar: true
asarUnpack:
  - assets/**
  - '*.mp3'
  - '*.wav'
extraResources:
  - filter:
      - assets/**
win:
  target:
    - target: nsis
      arch:
        - x64
  icon: assets/icon.ico
  requestedExecutionLevel: asInvoker
  artifactName: ${productName}-${version}-Setup.${ext}
nsis:
  oneClick: false
  perMachine: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: نظام الجرس المدرسي
  displayLanguageSelector: false
  installerLanguages:
    - ar
  language: '1025'
  deleteAppDataOnUninstall: false
  allowElevation: true
  runAfterFinish: true
publish: null
electronVersion: 28.0.0
