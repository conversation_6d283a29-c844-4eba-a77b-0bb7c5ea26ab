{"name": "school-bell-system", "version": "1.0.0", "description": "نظام الجرس المدرسي الإلكتروني", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "build-win": "electron-builder --win", "dist": "npm run build-win", "pack": "electron-builder --dir"}, "keywords": ["school", "bell", "system", "arabic", "education"], "author": {"name": "محم<PERSON> الشوامرة", "email": "moham<PERSON>@example.com"}, "license": "MIT", "devDependencies": {"electron": "28.0.0", "electron-builder": "24.9.1", "electron-packager": "^17.1.2"}, "dependencies": {"lowdb": "^6.1.1", "xlsx": "^0.18.5"}}