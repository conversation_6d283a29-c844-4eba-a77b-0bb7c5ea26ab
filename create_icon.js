const fs = require('fs');
const path = require('path');

// Create a simple SVG icon
const svgIcon = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bellGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2196F3;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="128" cy="128" r="120" fill="url(#bellGradient)" stroke="#fff" stroke-width="8"/>
  
  <!-- Bell shape -->
  <path d="M128 60 C100 60, 80 80, 80 110 L80 140 C80 150, 70 160, 60 160 L196 160 C186 160, 176 150, 176 140 L176 110 C176 80, 156 60, 128 60 Z" fill="#fff" opacity="0.9"/>
  
  <!-- Bell handle -->
  <rect x="120" y="45" width="16" height="20" rx="8" fill="#fff" opacity="0.9"/>
  
  <!-- Bell clapper -->
  <circle cx="128" cy="150" r="8" fill="#fff" opacity="0.8"/>
  
  <!-- Sound waves -->
  <path d="M200 100 Q220 120, 200 140" stroke="#fff" stroke-width="4" fill="none" opacity="0.6"/>
  <path d="M210 90 Q235 120, 210 150" stroke="#fff" stroke-width="3" fill="none" opacity="0.4"/>
  <path d="M56 100 Q36 120, 56 140" stroke="#fff" stroke-width="4" fill="none" opacity="0.6"/>
  <path d="M46 90 Q21 120, 46 150" stroke="#fff" stroke-width="3" fill="none" opacity="0.4"/>
  
  <!-- Bottom notification -->
  <path d="M110 170 C110 180, 118 190, 128 190 C138 190, 146 180, 146 170" stroke="#fff" stroke-width="3" fill="none" opacity="0.8"/>
</svg>`;

// Write SVG file
fs.writeFileSync(path.join(__dirname, 'assets', 'icon.svg'), svgIcon);

console.log('SVG icon created successfully!');

// Create a simple ICO file (16x16, 32x32, 48x48 sizes)
// This is a basic ICO file structure with a simple bell icon
const createSimpleIco = () => {
  // ICO file header (6 bytes)
  const header = Buffer.alloc(6);
  header.writeUInt16LE(0, 0);     // Reserved (must be 0)
  header.writeUInt16LE(1, 2);     // Image type (1 = ICO)
  header.writeUInt16LE(4, 4);     // Number of images

  // Create 4 icon directory entries (16 bytes each)
  const entries = Buffer.alloc(64); // 4 entries × 16 bytes
  
  // Entry 1: 16x16
  entries.writeUInt8(16, 0);      // Width
  entries.writeUInt8(16, 1);      // Height
  entries.writeUInt8(0, 2);       // Color palette
  entries.writeUInt8(0, 3);       // Reserved
  entries.writeUInt16LE(1, 4);    // Color planes
  entries.writeUInt16LE(32, 6);   // Bits per pixel
  entries.writeUInt32LE(1128, 8); // Image size
  entries.writeUInt32LE(54, 12);  // Image offset

  // Entry 2: 32x32
  entries.writeUInt8(32, 16);     // Width
  entries.writeUInt8(32, 17);     // Height
  entries.writeUInt8(0, 18);      // Color palette
  entries.writeUInt8(0, 19);      // Reserved
  entries.writeUInt16LE(1, 20);   // Color planes
  entries.writeUInt16LE(32, 22);  // Bits per pixel
  entries.writeUInt32LE(4264, 24); // Image size
  entries.writeUInt32LE(1182, 28); // Image offset

  // Entry 3: 48x48
  entries.writeUInt8(48, 32);     // Width
  entries.writeUInt8(48, 33);     // Height
  entries.writeUInt8(0, 34);      // Color palette
  entries.writeUInt8(0, 35);      // Reserved
  entries.writeUInt16LE(1, 36);   // Color planes
  entries.writeUInt16LE(32, 38);  // Bits per pixel
  entries.writeUInt32LE(9304, 40); // Image size
  entries.writeUInt32LE(5446, 44); // Image offset

  // Entry 4: 256x256
  entries.writeUInt8(0, 48);      // Width (0 means 256)
  entries.writeUInt8(0, 49);      // Height (0 means 256)
  entries.writeUInt8(0, 50);      // Color palette
  entries.writeUInt8(0, 51);      // Reserved
  entries.writeUInt16LE(1, 52);   // Color planes
  entries.writeUInt16LE(32, 54);  // Bits per pixel
  entries.writeUInt32LE(262184, 56); // Image size
  entries.writeUInt32LE(14750, 60); // Image offset

  // Create simple bitmap data for each size
  const create16x16 = () => {
    const bmp = Buffer.alloc(1128);
    // BMP header
    bmp.writeUInt32LE(40, 0);      // Header size
    bmp.writeInt32LE(16, 4);       // Width
    bmp.writeInt32LE(32, 8);       // Height (double for AND mask)
    bmp.writeUInt16LE(1, 12);      // Planes
    bmp.writeUInt16LE(32, 14);     // Bits per pixel
    
    // Simple bell pattern (blue background with white bell)
    for (let y = 0; y < 16; y++) {
      for (let x = 0; x < 16; x++) {
        const offset = 40 + (15 - y) * 64 + x * 4;
        if ((x >= 4 && x <= 11 && y >= 3 && y <= 10) || 
            (x >= 6 && x <= 9 && y >= 1 && y <= 2)) {
          // White bell
          bmp.writeUInt32LE(0xFFFFFFFF, offset);
        } else {
          // Blue background
          bmp.writeUInt32LE(0xFF2196F3, offset);
        }
      }
    }
    return bmp;
  };

  const create32x32 = () => {
    const bmp = Buffer.alloc(4264);
    // BMP header
    bmp.writeUInt32LE(40, 0);      // Header size
    bmp.writeInt32LE(32, 4);       // Width
    bmp.writeInt32LE(64, 8);       // Height (double for AND mask)
    bmp.writeUInt16LE(1, 12);      // Planes
    bmp.writeUInt16LE(32, 14);     // Bits per pixel
    
    // Simple bell pattern
    for (let y = 0; y < 32; y++) {
      for (let x = 0; x < 32; x++) {
        const offset = 40 + (31 - y) * 128 + x * 4;
        if ((x >= 8 && x <= 23 && y >= 6 && y <= 20) || 
            (x >= 12 && x <= 19 && y >= 2 && y <= 5)) {
          // White bell
          bmp.writeUInt32LE(0xFFFFFFFF, offset);
        } else {
          // Blue background
          bmp.writeUInt32LE(0xFF2196F3, offset);
        }
      }
    }
    return bmp;
  };

  const create48x48 = () => {
    const bmp = Buffer.alloc(9304);
    // BMP header
    bmp.writeUInt32LE(40, 0);      // Header size
    bmp.writeInt32LE(48, 4);       // Width
    bmp.writeInt32LE(96, 8);       // Height (double for AND mask)
    bmp.writeUInt16LE(1, 12);      // Planes
    bmp.writeUInt16LE(32, 14);     // Bits per pixel
    
    // Simple bell pattern
    for (let y = 0; y < 48; y++) {
      for (let x = 0; x < 48; x++) {
        const offset = 40 + (47 - y) * 192 + x * 4;
        if ((x >= 12 && x <= 35 && y >= 9 && y <= 30) || 
            (x >= 18 && x <= 29 && y >= 3 && y <= 8)) {
          // White bell
          bmp.writeUInt32LE(0xFFFFFFFF, offset);
        } else {
          // Blue background
          bmp.writeUInt32LE(0xFF2196F3, offset);
        }
      }
    }
    return bmp;
  };

  const create256x256 = () => {
    const bmp = Buffer.alloc(262184);
    // BMP header
    bmp.writeUInt32LE(40, 0);      // Header size
    bmp.writeInt32LE(256, 4);      // Width
    bmp.writeInt32LE(512, 8);      // Height (double for AND mask)
    bmp.writeUInt16LE(1, 12);      // Planes
    bmp.writeUInt16LE(32, 14);     // Bits per pixel

    // Simple bell pattern
    for (let y = 0; y < 256; y++) {
      for (let x = 0; x < 256; x++) {
        const offset = 40 + (255 - y) * 1024 + x * 4;
        if ((x >= 64 && x <= 191 && y >= 48 && y <= 160) ||
            (x >= 96 && x <= 159 && y >= 16 && y <= 47)) {
          // White bell
          bmp.writeUInt32LE(0xFFFFFFFF, offset);
        } else {
          // Blue background
          bmp.writeUInt32LE(0xFF2196F3, offset);
        }
      }
    }
    return bmp;
  };

  // Combine all parts
  const ico = Buffer.concat([
    header,
    entries,
    create16x16(),
    create32x32(),
    create48x48(),
    create256x256()
  ]);

  return ico;
};

// Write ICO file
const icoData = createSimpleIco();
fs.writeFileSync(path.join(__dirname, 'assets', 'icon.ico'), icoData);

console.log('ICO icon created successfully!');
console.log('Icon files created in assets/ directory');
