<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الجرس المدرسي الإلكتروني</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            margin: 0;
            padding: 0;
            direction: rtl;
            color: white;
            overflow: hidden;
        }

        .main-container {
            text-align: center;
            padding: 20px 20px 80px 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
            margin: 0;
            border-radius: 0;
            border: none;
            box-shadow: none;
            box-sizing: border-box;
        }

        .main-container::-webkit-scrollbar {
            width: 12px;
        }

        .main-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .main-container::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 10px;
        }

        .main-container::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .content-wrapper {
            margin: 20px 0;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .control-panel {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            flex-wrap: wrap;
        }

        .control-btn {
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(33, 150, 243, 0.4);
        }

        .control-btn.settings {
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }

        .control-btn.settings:hover {
            box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
        }

        .control-btn.emergency {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
        }

        .control-btn.emergency:hover {
            box-shadow: 0 6px 16px rgba(244, 67, 54, 0.4);
        }

        .control-btn.emergency.active {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            animation: pulse 1s infinite;
        }

        .control-btn.refresh {
            background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
            box-shadow: 0 4px 12px rgba(156, 39, 176, 0.3);
        }

        .control-btn.refresh:hover {
            box-shadow: 0 6px 16px rgba(156, 39, 176, 0.4);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* شريط التمرير العمودي */
        .scroll-controls {
            position: fixed;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 8px;
            z-index: 1000;
        }

        .scroll-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            color: white;
            font-size: 1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .scroll-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
        }

        .scroll-btn:active {
            transform: scale(0.95);
        }

        .scroll-btn.up {
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
        }

        .scroll-btn.down {
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
        }

        .scroll-btn.top {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        }

        .scroll-btn.bottom {
            background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
        }

        /* مؤشر موقع التمرير */
        .scroll-indicator {
            position: fixed;
            left: 15px;
            bottom: 15px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 8px 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            font-size: 0.8rem;
            color: white;
            z-index: 1000;
        }

        .scroll-progress {
            width: 3px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            position: fixed;
            left: 70px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 999;
        }

        .scroll-progress-bar {
            width: 100%;
            background: linear-gradient(to bottom, #4caf50, #2196f3);
            border-radius: 2px;
            transition: height 0.3s ease;
            height: 0%;
        }





        .alerts-section {
            margin-top: 20px;
        }

        .alerts-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .header-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .dropdown-btn {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .dropdown-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(255, 152, 0, 0.4);
        }

        .settings-btn {
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
        }

        .settings-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(33, 150, 243, 0.4);
        }

        .table-container {
            transition: all 0.5s ease;
            overflow: hidden;
            max-height: 1000px;
            opacity: 1;
        }

        .table-container.hidden {
            max-height: 0;
            opacity: 0;
            margin: 0;
            padding: 0;
        }

        .app-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .app-subtitle {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.9;
            font-weight: 300;
        }

        .clock-container {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .clock {
            font-size: 2rem;
            font-weight: 700;
            font-family: 'Courier New', monospace;
            letter-spacing: 0.1em;
            text-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
            margin-bottom: 8px;
        }

        .date {
            font-size: 0.9rem;
            opacity: 0.8;
            font-weight: 400;
        }

        /* مستطيل الحصص الجارية */
        .current-classes-header {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.2) 0%, rgba(67, 160, 71, 0.15) 100%);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(76, 175, 80, 0.4);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.2);
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
            color: rgba(255, 255, 255, 0.95);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .classes-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
            margin-top: 12px;
        }

        .class-item {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
            border-radius: 12px;
            padding: 18px;
            border: 1px solid rgba(255, 255, 255, 0.4);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .class-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4caf50, #2196f3, #ff9800);
            border-radius: 15px 15px 0 0;
        }

        .class-item:hover {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.35) 0%, rgba(255, 255, 255, 0.25) 100%);
            transform: translateY(-5px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
        }

        .class-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .class-name {
            font-size: 1.1rem;
            font-weight: 700;
            color: #fff;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .class-name::before {
            content: '📚';
            font-size: 1rem;
        }

        .class-time {
            font-size: 0.85rem;
            font-weight: 600;
            color: #fff;
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            padding: 6px 12px;
            border-radius: 20px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
            box-shadow: 0 2px 6px rgba(255, 152, 0, 0.3);
        }

        .class-details {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin-top: 15px;
        }

        .teacher-only {
            grid-column: 1 / -1;
            text-align: center;
        }

        .class-detail-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 12px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .class-detail-label {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .class-detail-value {
            font-size: 1rem;
            font-weight: 600;
            color: #fff;
        }

        .teacher-name {
            color: #e3f2fd;
        }

        .class-grade {
            color: #f3e5f5;
        }

        .class-status {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        }

        .no-classes {
            text-align: center;
            padding: 40px;
            color: rgba(255, 255, 255, 0.7);
            font-style: italic;
            font-size: 1.2rem;
            grid-column: 1 / -1;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            border: 2px dashed rgba(255, 255, 255, 0.3);
        }

        .no-classes-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
            display: block;
        }

        .no-classes-message {
            font-size: 1.1rem;
            margin-bottom: 10px;
        }

        .no-classes-submessage {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* جدول التنبيهات */
        .alerts-container {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            height: 100%;
        }

        .alerts-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .alerts-table th,
        .alerts-table td {
            padding: 15px 10px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.15);
            vertical-align: middle;
        }

        .alerts-table th {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
            font-weight: 700;
            color: #fff;
            font-size: 1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .alerts-table td {
            color: rgba(255, 255, 255, 0.95);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .alerts-table tr:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        .alerts-table tbody tr {
            transition: all 0.3s ease;
        }

        .alerts-table tbody tr:nth-child(even) {
            background: rgba(255, 255, 255, 0.05);
        }

        .alert-type {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .alert-start {
            background: rgba(76, 175, 80, 0.3);
            color: #a5d6a7;
        }

        .alert-end {
            background: rgba(244, 67, 54, 0.3);
            color: #ffcdd2;
        }

        .alert-break {
            background: rgba(255, 193, 7, 0.3);
            color: #fff3c4;
        }

        .sound-btn {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .sound-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(255, 152, 0, 0.4);
        }

        .emergency-section {
            margin-top: 20px;
            grid-column: 1 / -1;
        }

        .emergency-btn {
            background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 1.2rem;
            font-weight: 600;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(255, 71, 87, 0.4);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .emergency-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(255, 71, 87, 0.6);
            background: linear-gradient(135deg, #ff3838 0%, #ff2f2f 100%);
        }

        .emergency-btn:active {
            transform: translateY(-1px);
        }

        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            text-align: center;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.85rem;
            padding: 10px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
        }

        .footer p {
            margin: 2px 0;
        }

        .copyright {
            font-weight: 600;
            color: #fff;
        }

        /* تأثيرات متحركة */
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        .clock {
            animation: pulse 2s infinite;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .main-container {
            animation: fadeIn 1s ease-out;
        }

        /* تأثير الخلفية المتحركة */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
            opacity: 0.3;
        }

        /* استجابة للشاشات الصغيرة */
        @media (max-width: 1024px) {
            .main-container {
                width: 98%;
                padding: 15px;
            }

            .control-panel {
                flex-direction: column;
                gap: 10px;
            }

            .control-btn {
                width: 100%;
                justify-content: center;
            }
        }

        @media (max-width: 768px) {
            .app-title {
                font-size: 1.8rem;
            }

            .clock {
                font-size: 2.2rem;
            }

            .main-container {
                padding: 15px;
                min-height: 95vh;
            }

            .classes-list {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .class-item {
                padding: 20px;
            }

            .class-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .class-details {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .section-title {
                font-size: 1.1rem;
            }

            .class-name {
                font-size: 1.2rem;
            }

            .class-time {
                font-size: 0.9rem;
                padding: 6px 12px;
            }

            .class-status {
                font-size: 0.7rem;
                padding: 4px 8px;
            }

            .alerts-table th,
            .alerts-table td {
                padding: 8px 4px;
                font-size: 0.8rem;
            }

            .sound-btn {
                padding: 4px 8px;
                font-size: 0.7rem;
            }

            .header-controls {
                flex-direction: column;
                gap: 8px;
                align-items: stretch;
            }

            .dropdown-btn,
            .settings-btn {
                font-size: 0.8rem;
                padding: 8px 15px;
            }

            .control-btn {
                font-size: 0.9rem;
                padding: 10px 20px;
            }

            .scroll-controls {
                left: 10px;
                gap: 8px;
            }

            .scroll-btn {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }

            .scroll-indicator {
                left: 10px;
                bottom: 10px;
                padding: 8px 12px;
                font-size: 0.8rem;
            }

            .scroll-progress {
                left: 65px;
                height: 80px;
            }
        }
        @keyframes flashAlert {
            0%,100% { background: #fff700; color: #000; }
            50% { background: #ff3838; color: #fff; }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- إطار حديث أعلى البرنامج -->
        <div id="customTitleBar" style="height:38px;display:flex;align-items:center;justify-content:space-between;background:linear-gradient(90deg,#764ba2 0%,#667eea 100%);padding:0 12px 0 0;border-radius:0 0 18px 18px;box-shadow:0 2px 12px #0002;user-select:none;">
            <div style="font-size:1.2rem;font-weight:700;color:#fff;letter-spacing:1px;display:flex;align-items:center;gap:8px;">
                <span style="font-size:1.5rem;">🔔</span> الجرس المدرسي
            </div>
            <div style="display:flex;align-items:center;gap:2px;">
                <button onclick="window.electronAPI.minimize()" title="تصغير" style="background:none;border:none;color:#fff;font-size:1.3rem;padding:2px 10px;cursor:pointer;border-radius:6px 0 0 6px;">&#8211;</button>
                <button onclick="window.electronAPI.maximize()" title="تكبير/استعادة" style="background:none;border:none;color:#fff;font-size:1.1rem;padding:2px 10px;cursor:pointer;">&#9723;</button>
                <button onclick="window.electronAPI.close()" title="إغلاق" style="background:none;border:none;color:#fff;font-size:1.3rem;padding:2px 14px;cursor:pointer;border-radius:0 6px 6px 0;background:#f44336;">&#10005;</button>
            </div>
        </div>
    <script>
    // دعم أزرار الإطار الحديث - استخدام electronAPI من preload
    // window.electronAPI متاح من خلال preload.js
    if (!window.electronAPI) {
        console.error('electronAPI not available - preload script may not be loaded');
        // Fallback for development
        window.electronAPI = {
            minimize: () => console.log('minimize called'),
            maximize: () => console.log('maximize called'),
            close: () => console.log('close called'),
            dbGet: (key) => Promise.resolve(localStorage.getItem(key)),
            dbSet: (key, value) => Promise.resolve(localStorage.setItem(key, value))
        };
    }
    </script>
        <div id="schoolInfo" style="margin-bottom: 10px; text-align: center;"></div>
        <!-- العنوان والساعة -->
        <div class="header-section">
            <h1 class="app-title">🔔 نظام الجرس المدرسي الإلكتروني</h1>
            <p class="app-subtitle">نظام متطور لإدارة أوقات الحصص والتنبيهات المدرسية</p>

            <div class="clock-container">
                <div class="clock" id="currentTime">--:--:--</div>
                <div class="date" id="currentDate">جاري التحميل...</div>
                <div id="schoolInfoSide" style="font-size:1.05rem;font-weight:600;color:#fff;opacity:0.93;min-width:180px;text-align:right;"></div>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="content-wrapper">
            <!-- لوحة التحكم العلوية -->
            <div class="control-panel">
                <button class="control-btn settings" onclick="openSettings()">
                    ⚙️ الإعدادات
                </button>
                <button class="control-btn emergency" id="emergencyBtn" onclick="toggleEmergency()">
                    <span id="emergencyIcon">🚨</span>
                    <span id="emergencyText">تشغيل الطوارئ</span>
                </button>
                <button class="control-btn refresh" onclick="refreshData()">
                    🔄 تحديث البيانات
                </button>
            </div>

            <!-- مستطيل الحصص الجارية -->
            <div class="current-classes-header">
                <h3 class="section-title">📚 الحصص الجارية الآن</h3>
                <div class="classes-list" id="currentClassesList">
                    <!-- سيتم إدراج الحصص هنا تلقائياً -->
                </div>
            </div>

            <!-- جدول التنبيهات -->
            <div class="alerts-section">
                <div class="alerts-container">
                    <!-- جدول التنبيهات -->
                        <div class="alerts-header">
                            <h3 class="section-title">🔔 جدول الاستراحات والأجراس</h3>
                            <div class="header-controls">
                                <button class="dropdown-btn" onclick="toggleTableVisibility()">
                                    <span id="dropdownIcon">▼</span> عرض/إخفاء الجدول
                                </button>
                                <button class="settings-btn" onclick="openSettings()">⚙️ الإعدادات</button>
                            </div>
                        </div>

                        <div id="alertsTableContainer" class="table-container">
                            <table class="alerts-table">
                                <thead>
                                    <tr>
                                        <th>نوع التنبيه</th>
                                        <th>الوقت</th>
                                        <th>المادة</th>
                                        <th>اليوم</th>
                                        <th>تشغيل الصوت</th>
                                    </tr>
                                </thead>
                                <tbody id="alertsTableBody">
                                    <!-- سيتم إدراج التنبيهات هنا تلقائياً -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>


    <!-- شريط التمرير العمودي -->
    <!-- تم حذف أزرار التحريك -->

    <!-- مؤشر التقدم -->
    <div class="scroll-progress">
        <div class="scroll-progress-bar" id="scrollProgressBar"></div>
    </div>

    <!-- مؤشر موقع التمرير -->
    <div class="scroll-indicator" id="scrollIndicator">
        📍 الأعلى
    </div>

    <div class="footer">
        <p class="copyright">© محمد الشوامرة - 0566000140</p>
        <p>جميع الحقوق محفوظة</p>
    </div>

    <script>
        // عرض اسم المدرسة والمدير من localStorage
// عرض اسم المدرسة والمدير بجانب الساعة مباشرة
function renderSchoolInfo() {
    const schoolName = localStorage.getItem('schoolName') || '';
    const managerName = localStorage.getItem('managerName') || '';
    let side = '';
    if (schoolName) side += `<span style="font-size:1.05rem;font-weight:700;">${schoolName}</span>`;
    if (managerName) side += `<br><span style="font-size:0.95rem;font-weight:500;">${managerName}</span>`;
    document.getElementById('schoolInfoSide').innerHTML = side;
}
renderSchoolInfo();
        // بيانات التنبيهات التجريبية (استراحات فقط)
        const sampleAlerts = [
            {
                id: 1,
                type: "break",
                time: "09:45",
                subject: "استراحة الفطور",
                grade: "جميع الصفوف",
                day: "جميع الأيام",
                sound: "bell-break.mp3"
            },
            {
                id: 2,
                type: "break",
                time: "12:00",
                subject: "استراحة الغداء",
                grade: "جميع الصفوف",
                day: "جميع الأيام",
                sound: "bell-break.mp3"
            },
            {
                id: 3,
                type: "break",
                time: "14:30",
                subject: "نهاية اليوم الدراسي",
                grade: "جميع الصفوف",
                day: "جميع الأيام",
                sound: "bell-end.mp3"
            }
        ];

        // جلب الحصص من localStorage أو بيانات تجريبية
        function getSchedule() {
            let classes = [];
            try {
                classes = JSON.parse(localStorage.getItem('classes') || '[]');
            } catch {}
            if (!classes || !Array.isArray(classes) || classes.length === 0) {
                return [];
            }
            return classes;
        }

        // تحديث الساعة والتاريخ
        function toEnglishDigits(str) {
            return str.replace(/[٠-٩]/g, d => '0123456789'['٠١٢٣٤٥٦٧٨٩'.indexOf(d)]);
        }
        function updateDateTime() {
            const now = new Date();
            // الوقت بصيغة 12 ساعة وأرقام إنجليزية
            let timeString = now.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: true
            });
            // التاريخ ميلادي وأرقام إنجليزية
            let dateString = now.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            document.getElementById('currentTime').textContent = timeString;
            document.getElementById('currentDate').textContent = dateString;
            updateCurrentClasses();
            updateAlertsTable();
        }

        // دالة للتحقق من الحصص الجارية
        function updateCurrentClasses() {
            const now = new Date();
            const currentTime = now.toTimeString().slice(0, 5); // HH:MM
            const currentDay = now.toLocaleDateString('ar-SA', { weekday: 'long' });

            const schedule = getSchedule();
            let currentClasses = schedule.filter(classItem => {
                if (!classItem.days || !classItem.days.includes(currentDay)) return false;
                return currentTime >= classItem.startTime && currentTime <= classItem.endTime;
            });
            renderCurrentClasses(currentClasses);
        }

        // عرض الحصص الجارية
        function renderCurrentClasses(classes) {
            const container = document.getElementById('currentClassesList');
            if (classes.length === 0) {
                container.innerHTML = `
                    <div class="no-classes">
                        <span class="no-classes-icon">📚</span>
                        <div class="no-classes-message">لا توجد حصص جارية في الوقت الحالي</div>
                        <div class="no-classes-submessage">سيتم عرض الحصص النشطة هنا تلقائياً عند بدء أي حصة</div>
                    </div>
                `;
                return;
            }
            // كل حصة جارية في مربع منفصل صغير
            container.innerHTML = `<div style='display:flex;flex-wrap:wrap;gap:8px;justify-content:center;'>` +
                classes.map(classItem => `
                <div style="width:3cm;height:3cm;min-width:3cm;min-height:3cm;max-width:3cm;max-height:3cm;background:rgba(255,255,255,0.18);border-radius:12px;box-shadow:0 1px 4px #0002;padding:6px 4px;display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;overflow:auto;font-size:0.85em;">
                    <div style='font-size:0.9em;margin-bottom:2px;'>🟢</div>
                    <div style="font-weight:bold;font-size:1em;color:#111;">${classItem.className}</div>
                    <div style="font-size:0.9em;font-weight:bold;color:#111;">${classItem.subjectName || ''} | ${classItem.gradeName || classItem.classGrade || ''}</div>
                    <div style="font-size:0.85em;font-weight:bold;color:#111;">${classItem.teacherName}</div>
                    <div style="font-size:0.8em;font-weight:bold;color:#111;">${classItem.startTime} - ${classItem.endTime}</div>
                    <div style="font-size:0.8em;font-weight:bold;color:#111;">${(classItem.days || []).join('، ')}</div>
                </div>
                `).join('') + `</div>`;
        }
        // زر تشغيل صوت الحصة
        function playClassSound(soundUrl) {
            if (!soundUrl) return alert('لا يوجد صوت لهذه الحصة');
            const audio = new Audio(soundUrl);
            audio.play();
        }

        // عرض جدول التنبيهات من localStorage
        function updateAlertsTable() {
            const tableBody = document.getElementById('alertsTableBody');
            let alerts = [];
            try {
                alerts = JSON.parse(localStorage.getItem('alerts') || '[]');
            } catch {}
            tableBody.innerHTML = alerts.length === 0 ? '<tr><td colspan="5">لا توجد تنبيهات مضافة</td></tr>' :
                alerts.map((alert, idx) => {
                    const typeText = alert.type === 'break' ? 'استراحة' :
                        alert.type === 'end' ? 'نهاية اليوم' :
                        alert.type === 'assembly' ? 'الطابور الصباحي' : 'تنبيه';
                    const typeClass = `alert-${alert.type}`;
                    return `
                        <tr id="alertRow${idx}">
                            <td><span class="alert-type ${typeClass}">${typeText}</span></td>
                            <td><strong>${alert.time}</strong></td>
                            <td>${alert.subject}</td>
                            <td>${(alert.days||[]).join('، ')}</td>
                            <td>
                                <button class="sound-btn" onclick="playAlertSound('${alert.sound || ''}')">🔊 تشغيل</button>
                            </td>
                        </tr>
                    `;
                }).join('');
        }

        // تشغيل صوت التنبيه من ملف
        function playAlertSound(soundUrl) {
            if (!soundUrl) return alert('لا يوجد صوت لهذا التنبيه');
            const audio = new Audio(soundUrl);
            audio.play();
        }

        // فحص التنبيهات التلقائية مع وميض وصوت
        function checkAutoAlerts() {
            const now = new Date();
            const currentTime = now.toTimeString().slice(0, 5); // HH:MM
            const currentDay = now.toLocaleDateString('ar-SA', { weekday: 'long' });
            let alerts = [];
            try {
                alerts = JSON.parse(localStorage.getItem('alerts') || '[]');
            } catch {}
            alerts.forEach((alert, idx) => {
                if (alert.time === currentTime && alert.days && alert.days.includes(currentDay)) {
                    // تشغيل الصوت
                    playAlertSound(alert.sound);
                    // وميض الصف في الجدول
                    const row = document.getElementById('alertRow'+idx);
                    if (row) {
                        row.style.animation = 'flashAlert 1s 5';
                        setTimeout(()=>{row.style.animation='';}, 5000);
                    }
                    // إشعار سطح المكتب
                    if (Notification.permission === 'granted') {
                        new Notification(`تنبيه: ${alert.subject}`, {
                            body: `${alert.type === 'start' ? 'بداية' : alert.type === 'end' ? 'نهاية' : alert.type === 'assembly' ? 'الطابور الصباحي' : 'استراحة'} - ${(alert.days||[]).join(', ')}`,
                            icon: 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y=".9em" font-size="90">🔔</text></svg>'
                        });
                    }
                }
            });
        }
        // وميض للصف في جدول التنبيهات
        // (تم نقل keyframes إلى CSS)

        // تشغيل تحديث الوقت كل ثانية
        setInterval(() => {
            updateDateTime();
            checkAutoAlerts(); // فحص التنبيهات التلقائية
        }, 1000);

        updateDateTime(); // تحديث فوري عند التحميل

        // دالة الطوارئ
        function triggerEmergency() {
            // تأثير بصري للطوارئ
            document.body.style.background = 'linear-gradient(135deg, #ff4757 0%, #ff3838 100%)';
            
            // رسالة تأكيد
            const confirmed = confirm('🚨 تنبيه طوارئ!\n\nهل أنت متأكد من تفعيل إنذار الطوارئ؟\nسيتم إرسال تنبيه لجميع الفصول.');
            
            if (confirmed) {
                alert('🚨 تم تفعيل إنذار الطوارئ!\n\nتم إرسال التنبيه لجميع الفصول والإدارة.');
                
                // محاكاة صوت الإنذار
                playEmergencySound();
                
                // إعادة اللون الطبيعي بعد 5 ثوان
                setTimeout(() => {
                    document.body.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                }, 5000);
            } else {
                // إعادة اللون الطبيعي فوراً
                document.body.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            }
        }

        // محاكاة صوت الطوارئ
        let emergencyInterval = null;
        function playEmergencySound() {
            // تشغيل صوت الطوارئ بشكل دوري
            let emergencyVolume = 1.0;
            try {
                emergencyVolume = parseFloat(localStorage.getItem('emergencyVolume') || '1.0');
            } catch {}
            if (emergencyInterval) clearInterval(emergencyInterval);
            emergencyInterval = setInterval(() => {
                const audio = new Audio('assets/emergency.mp3');
                audio.volume = emergencyVolume;
                audio.play();
            }, 2000);
        }
        function stopEmergencySound() {
            if (emergencyInterval) {
                clearInterval(emergencyInterval);
                emergencyInterval = null;
            }
        }

        // متغيرات لحالة الواجهة
        let isTableVisible = true;
        let isEmergencyActive = false;
        let scrollSpeed = 300; // سرعة التمرير بالبكسل

        // تبديل عرض/إخفاء الجدول
        function toggleTableVisibility() {
            const tableContainer = document.getElementById('alertsTableContainer');
            const dropdownIcon = document.getElementById('dropdownIcon');

            if (isTableVisible) {
                tableContainer.classList.add('hidden');
                dropdownIcon.textContent = '▲';
                isTableVisible = false;
                console.log('📋 تم إخفاء جدول التنبيهات');
            } else {
                tableContainer.classList.remove('hidden');
                dropdownIcon.textContent = '▼';
                isTableVisible = true;
                console.log('📋 تم إظهار جدول التنبيهات');
            }
        }

        // تبديل حالة الطوارئ
        function toggleEmergency() {
            const emergencyBtn = document.getElementById('emergencyBtn');
            const emergencyIcon = document.getElementById('emergencyIcon');
            const emergencyText = document.getElementById('emergencyText');

            if (isEmergencyActive) {
                // إيقاف الطوارئ
                emergencyBtn.classList.remove('active');
                emergencyIcon.textContent = '🚨';
                emergencyText.textContent = 'تشغيل الطوارئ';
                isEmergencyActive = false;
                document.body.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                stopEmergencySound();
                console.log('🚨 تم إيقاف حالة الطوارئ');
                alert('تم إيقاف حالة الطوارئ');
            } else {
                // تفعيل الطوارئ
                emergencyBtn.classList.add('active');
                emergencyIcon.textContent = '⏹️';
                emergencyText.textContent = 'إيقاف الطوارئ';
                isEmergencyActive = true;
                document.body.style.background = 'linear-gradient(135deg, #ff4757 0%, #ff3838 100%)';
                playEmergencySound();
                console.log('🚨 تم تفعيل حالة الطوارئ');
                alert('🚨 تم تفعيل إنذار الطوارئ!\n\nتم إرسال التنبيه لجميع الفصول والإدارة.');
            }
        }

        // تحديث البيانات
        function refreshData() {
            console.log('🔄 بدء تحديث البيانات...');

            // تحديث الوقت والتاريخ
            updateDateTime();

            // تحديث الحصص الجارية
            updateCurrentClasses();

            // تحديث جدول التنبيهات
            updateAlertsTable();

            // تأثير بصري للتحديث
            const refreshBtn = document.querySelector('.control-btn.refresh');
            refreshBtn.style.transform = 'rotate(360deg)';
            setTimeout(() => {
                refreshBtn.style.transform = 'rotate(0deg)';
            }, 500);

            console.log('✅ تم تحديث البيانات بنجاح');

            // إشعار للمستخدم
            setTimeout(() => {
                alert('✅ تم تحديث جميع البيانات بنجاح!');
            }, 600);
        }

        // دوال التمرير
        function scrollUp() {
            window.scrollBy({
                top: -scrollSpeed,
                behavior: 'smooth'
            });
            console.log('⬆️ تمرير للأعلى');
        }

        function scrollDown() {
            window.scrollBy({
                top: scrollSpeed,
                behavior: 'smooth'
            });
            console.log('⬇️ تمرير للأسفل');
        }

        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
            console.log('🔝 الذهاب للأعلى');
        }

        function scrollToBottom() {
            window.scrollTo({
                top: document.body.scrollHeight,
                behavior: 'smooth'
            });
            console.log('🔚 الذهاب للأسفل');
        }

        // تحديث مؤشر التقدم ومؤشر الموقع
        function updateScrollIndicators() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / scrollHeight) * 100;

            // تحديث شريط التقدم
            const progressBar = document.getElementById('scrollProgressBar');
            progressBar.style.height = scrollPercent + '%';

            // تحديث مؤشر الموقع
            const indicator = document.getElementById('scrollIndicator');
            let position = '';

            if (scrollPercent < 10) {
                position = '📍 الأعلى';
            } else if (scrollPercent < 30) {
                position = '📍 الربع الأول';
            } else if (scrollPercent < 50) {
                position = '📍 المنتصف العلوي';
            } else if (scrollPercent < 70) {
                position = '📍 المنتصف السفلي';
            } else if (scrollPercent < 90) {
                position = '📍 الربع الأخير';
            } else {
                position = '📍 الأسفل';
            }

            indicator.textContent = position;
        }

        // مراقبة التمرير
        window.addEventListener('scroll', updateScrollIndicators);

        // تحديث المؤشرات عند تغيير حجم النافذة
        window.addEventListener('resize', updateScrollIndicators);

        // اختصارات لوحة المفاتيح للتمرير
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'ArrowUp':
                    e.preventDefault();
                    scrollUp();
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    scrollDown();
                    break;
                case 'Home':
                    e.preventDefault();
                    scrollToTop();
                    break;
                case 'End':
                    e.preventDefault();
                    scrollToBottom();
                    break;
                case 'PageUp':
                    e.preventDefault();
                    window.scrollBy({
                        top: -window.innerHeight * 0.8,
                        behavior: 'smooth'
                    });
                    break;
                case 'PageDown':
                    e.preventDefault();
                    window.scrollBy({
                        top: window.innerHeight * 0.8,
                        behavior: 'smooth'
                    });
                    break;
            }
        });

        // دعم التمرير بعجلة الماوس مع تحسينات
        let isScrolling = false;
        window.addEventListener('wheel', function(e) {
            if (!isScrolling) {
                isScrolling = true;
                setTimeout(() => {
                    isScrolling = false;
                }, 100);

                // تحديث المؤشرات بعد التمرير
                setTimeout(updateScrollIndicators, 50);
            }
        });

        // تحسين الأداء: تحديث المؤشرات بشكل محدود
        let scrollTimeout;
        function throttledUpdateScrollIndicators() {
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }
            scrollTimeout = setTimeout(updateScrollIndicators, 16); // 60fps
        }

        // استبدال مراقب التمرير العادي بالمحسن
        window.removeEventListener('scroll', updateScrollIndicators);
        window.addEventListener('scroll', throttledUpdateScrollIndicators);



        // فتح نافذة الإعدادات
        function openSettings() {
            const settingsWindow = window.open(
                'settings.html',
                'settingsWindow',
                'width=1000,height=700,resizable=yes,scrollbars=yes,menubar=no,toolbar=no,location=no,status=no'
            );

            if (settingsWindow) {
                settingsWindow.focus();
                console.log('🔧 تم فتح نافذة الإعدادات');
            } else {
                alert('فشل في فتح نافذة الإعدادات. تأكد من السماح للنوافذ المنبثقة.');
            }
        }

        // تأثير عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔔 تم تحميل نظام الجرس المدرسي الإلكتروني');

            // طلب إذن الإشعارات
            if ('Notification' in window && Notification.permission === 'default') {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        console.log('✅ تم منح إذن الإشعارات');
                    }
                });
            }

            // تحديث جدول التنبيهات عند التحميل
            updateAlertsTable();

            // تهيئة مؤشرات التمرير
            updateScrollIndicators();

            // رسالة ترحيب
            setTimeout(() => {
                console.log('✅ النظام جاهز للاستخدام');
                console.log('🔊 سيتم تشغيل التنبيهات تلقائياً في أوقاتها المحددة');
                console.log('🎛️ لوحة التحكم جاهزة ومتاحة');
                console.log('📜 شريط التمرير متاح ومفعل');
            }, 1000);
        });

        // منع النقر بالزر الأيمن (اختياري)
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            // F11 للشاشة الكاملة
            if (e.key === 'F11') {
                e.preventDefault();
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                } else {
                    document.documentElement.requestFullscreen();
                }
            }
            
            // Escape للخروج من الشاشة الكاملة
            if (e.key === 'Escape' && document.fullscreenElement) {
                document.exitFullscreen();
            }
        });
    </script>
</body>
</html>
