{"name": "lowdb", "version": "6.1.1", "description": "Tiny local JSON database for Node, Electron and the browser", "keywords": ["database", "db", "electron", "embed", "embedded", "flat", "JSON", "local", "localStorage", "sessionStorage", "browser", "esm"], "homepage": "https://github.com/typicode/lowdb#readme", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "repository": {"type": "git", "url": "git+https://github.com/typicode/lowdb.git"}, "funding": "https://github.com/sponsors/typicode", "license": "MIT", "author": "Typicode <<EMAIL>>", "type": "module", "exports": {".": "./lib/index.js", "./node": "./lib/node.js", "./browser": "./lib/browser.js"}, "types": "./lib", "typesVersions": {"*": {"node": ["lib/node.d.ts"], "browser": ["lib/browser.d.ts"]}}, "files": ["lib", "!lib/examples/**/*", "!lib/**/*.test.*"], "scripts": {"test": "xv --loader=ts-node/esm src", "lint": "eslint src --ext .ts --ignore-path .gitignore", "build": "del-cli lib && tsc", "prepublishOnly": "npm run build", "postversion": "git push --follow-tags && npm publish", "prepare": "husky install"}, "dependencies": {"steno": "^3.1.1"}, "devDependencies": {"@commitlint/cli": "^17.7.2", "@commitlint/config-conventional": "^17.7.0", "@commitlint/prompt-cli": "^17.7.2", "@sindresorhus/tsconfig": "^5.0.0", "@types/express": "^4.17.19", "@types/lodash": "^4.14.199", "@types/node": "^20.8.5", "@typicode/eslint-config": "^1.2.0", "del-cli": "^5.1.0", "eslint": "^8.51.0", "express-async-handler": "^1.2.0", "husky": "^8.0.3", "lodash": "^4.17.21", "tempy": "^3.1.0", "ts-node": "^10.9.1", "typescript": "^5.2.2", "xv": "^2.1.1"}, "engines": {"node": ">=16"}}