<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bellGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2196F3;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="128" cy="128" r="120" fill="url(#bellGradient)" stroke="#fff" stroke-width="8"/>
  
  <!-- Bell shape -->
  <path d="M128 60 C100 60, 80 80, 80 110 L80 140 C80 150, 70 160, 60 160 L196 160 C186 160, 176 150, 176 140 L176 110 C176 80, 156 60, 128 60 Z" fill="#fff" opacity="0.9"/>
  
  <!-- Bell handle -->
  <rect x="120" y="45" width="16" height="20" rx="8" fill="#fff" opacity="0.9"/>
  
  <!-- Bell clapper -->
  <circle cx="128" cy="150" r="8" fill="#fff" opacity="0.8"/>
  
  <!-- Sound waves -->
  <path d="M200 100 Q220 120, 200 140" stroke="#fff" stroke-width="4" fill="none" opacity="0.6"/>
  <path d="M210 90 Q235 120, 210 150" stroke="#fff" stroke-width="3" fill="none" opacity="0.4"/>
  <path d="M56 100 Q36 120, 56 140" stroke="#fff" stroke-width="4" fill="none" opacity="0.6"/>
  <path d="M46 90 Q21 120, 46 150" stroke="#fff" stroke-width="3" fill="none" opacity="0.4"/>
  
  <!-- Bottom notification -->
  <path d="M110 170 C110 180, 118 190, 128 190 C138 190, 146 180, 146 170" stroke="#fff" stroke-width="3" fill="none" opacity="0.8"/>
</svg>