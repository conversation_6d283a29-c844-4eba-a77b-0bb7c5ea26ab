<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الجرس المدرسي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            color: #fff;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
        }
        .settings-container {
            background: rgba(255,255,255,0.08);
            margin-top: 40px;
            padding: 30px 25px 20px 25px;
            border-radius: 18px;
            box-shadow: 0 8px 32px rgba(76, 175, 80, 0.15);
            width: 95%;
            max-width: 500px;
        }
        h2 {
            text-align: center;
            margin-bottom: 25px;
            font-size: 2rem;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
        }
        input, select {
            width: 100%;
            padding: 10px;
            margin-bottom: 18px;
            border-radius: 8px;
            border: none;
            font-size: 1rem;
            background: rgba(255,255,255,0.15);
            color: #fff;
        }
        button {
            width: 100%;
            padding: 12px;
            border-radius: 25px;
            border: none;
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
            color: #fff;
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            margin-top: 10px;
            transition: background 0.3s;
        }
        button:hover {
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #fff;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>

    <div class="settings-container">
        <h2>إعدادات الجرس المدرسي</h2>
        <form id="settingsForm">
            <div id="schoolManagerSection" style="display:none;flex-direction:column;gap:10px;margin-bottom:18px;">
                <label for="schoolName">اسم المدرسة:</label>
                <input type="text" id="schoolName" name="schoolName" placeholder="مثال: مدرسة المستقبل" required>
                <label for="managerName">اسم المدير:</label>
                <input type="text" id="managerName" name="managerName" placeholder="مثال: أ. محمد أحمد" required>
                <button type="button" id="saveGeneralSettingsBtn" style="width:auto;padding:8px 18px;font-size:1rem;background:linear-gradient(135deg,#4caf50 0%,#2196f3 100%);font-weight:700;">حفظ اسم المدرسة والمدير</button>
            </div>

            <label for="bellVolume">مستوى صوت الجرس:</label>
            <input type="range" id="bellVolume" name="bellVolume" min="0" max="100" value="70">

            <label for="themeColor">لون الواجهة الرئيسي:</label>
            <select id="themeColor" name="themeColor">
                <option value="ai">ألوان الذكاء الاصطناعي</option>
                <option value="blue">أزرق عصري</option>
                <option value="green">أخضر هادئ</option>
                <option value="dark">داكن</option>
            </select>

            <label for="emergencyVolume">مستوى صوت الطوارئ:</label>
            <input type="range" id="emergencyVolume" name="emergencyVolume" min="0" max="1" step="0.01" value="1" style="direction:ltr;">
            <span id="emergencyVolumeValue" style="font-size:1rem;margin-right:8px;">1.00</span>

            <hr style="margin: 25px 0; border: 0; border-top: 1px solid #fff2;">


            <h3 style="text-align:center;">إدارة الحصص</h3>
            <div id="classesList"></div>
            <label>إضافة حصة جديدة:</label>
            <input type="text" id="className" placeholder="اسم الحصة" required autofocus>
            <input type="text" id="subjectName" placeholder="المادة" required>
            <input type="text" id="gradeName" placeholder="الصف" required>
            <input type="text" id="teacherName" placeholder="اسم المعلم" required>
            <label>الأيام:</label>
            <div id="daysList" style="display:flex;flex-wrap:wrap;gap:8px 18px;margin-bottom:18px;">
                <label><input type="checkbox" name="days" value="السبت"> السبت</label>
                <label><input type="checkbox" name="days" value="الأحد"> الأحد</label>
                <label><input type="checkbox" name="days" value="الاثنين"> الاثنين</label>
                <label><input type="checkbox" name="days" value="الثلاثاء"> الثلاثاء</label>
                <label><input type="checkbox" name="days" value="الأربعاء"> الأربعاء</label>
                <label><input type="checkbox" name="days" value="الخميس"> الخميس</label>
                <label><input type="checkbox" name="days" value="الجمعة"> الجمعة</label>
            </div>
            <input type="time" id="startTime" required>
            <input type="time" id="endTime" required>
            <label>صوت الحصة (إجباري):</label>
            <!-- <input type="file" id="classSound" accept="audio/*" required> -->
            <button type="button" onclick="addClass()">إضافة الحصة</button>
            <hr style="margin: 18px 0; border: 0; border-top: 1px solid #fff2;">
            <label>رفع ملف حصص/أجراس (Excel):</label>
            <input type="file" id="excelUpload" accept=".xlsx,.xls" style="margin-bottom:12px;">
            <button type="button" onclick="importFromExcel()">استيراد من Excel</button>
        // استيراد الحصص/الأجراس من ملف Excel
        async function importFromExcel() {
            const input = document.getElementById('excelUpload');
            if (!input.files || input.files.length === 0) {
                alert('يرجى اختيار ملف Excel');
                return;
            }
            const file = input.files[0];
            const reader = new FileReader();
            reader.onload = async function(e) {
                try {
                    const XLSX = window.require ? window.require('xlsx') : null;
                    let workbook;
                    if (XLSX) {
                        workbook = XLSX.read(new Uint8Array(e.target.result), { type: 'array' });
                    } else {
                        alert('XLSX library not found.');
                        return;
                    }
                    const sheetName = workbook.SheetNames[0];
                    const sheet = workbook.Sheets[sheetName];
                    const data = XLSX.utils.sheet_to_json(sheet, { header: 1 });
                    let imported = 0;
                    const importedClasses = [];
                    for (let i = 1; i < data.length; i++) {
                        const row = data[i];
                        if (!row[0] || !row[1] || !row[2] || !row[3] || !row[4] || !row[5] || !row[6]) continue;
                        importedClasses.push({
                            className: row[0],
                            subjectName: row[1],
                            gradeName: row[2],
                            teacherName: row[3],
                            days: row[4].split(',').map(d=>d.trim()),
                            startTime: row[5],
                            endTime: row[6],
                            sound: row[7] || ''
                        });
                        imported++;
                    }
                    if (window.require) {
                        const { ipcRenderer } = require('electron');
                        // جلب الحصص الحالية من قاعدة البيانات
                        let dbClasses = await ipcRenderer.invoke('db-get', 'classes');
                        if (!Array.isArray(dbClasses)) dbClasses = [];
                        dbClasses = dbClasses.concat(importedClasses);
                        await ipcRenderer.invoke('db-set', 'classes', dbClasses);
                        classes = dbClasses;
                    } else {
                        classes = classes.concat(importedClasses);
                        localStorage.setItem('classes', JSON.stringify(classes));
                    }
                    renderClasses();
                    alert('تم استيراد ' + imported + ' حصة/جرس بنجاح!');
                } catch (err) {
                    alert('خطأ في قراءة الملف: ' + err.message);
                }
            };
            reader.readAsArrayBuffer(file);
        }

            <hr style="margin: 25px 0; border: 0; border-top: 1px solid #fff2;">

            <h3 style="text-align:center;">إدارة التنبيهات</h3>
            <div id="alertsList"></div>
            <label>إضافة تنبيه جديد:</label>
            <select id="alertType">
                <option value="start">بداية الحصة</option>
                <option value="end">نهاية الحصة</option>
                <option value="break">استراحة</option>
                <option value="assembly">الطابور الصباحي</option>
            </select>
            <input type="time" id="alertTime" required>
            <input type="text" id="alertSubject" placeholder="المادة/الوصف" required>
            <input type="text" id="alertDay" placeholder="اليوم" required>
            <label>اختر صوت التنبيه:</label>
            <input type="file" id="alertSound" accept="audio/*">
            <button type="button" onclick="addAlert()">إضافة التنبيه</button>

            <button type="submit" style="margin-top:30px;">حفظ الإعدادات</button>
        </form>
    </div>
    <div class="footer">
        <p>© محمد الشوامرة - جميع الحقوق محفوظة</p>
    </div>
    <script>
        // زر حفظ اسم المدرسة والمدير فقط
        // إظهار نموذج اسم المدرسة والمدير فقط إذا لم يكونا محفوظين
        function checkSchoolManager() {
            const schoolName = localStorage.getItem('schoolName');
            const managerName = localStorage.getItem('managerName');
            const section = document.getElementById('schoolManagerSection');
            if (!schoolName || !managerName) {
                section.style.display = 'flex';
            } else {
                section.style.display = 'none';
            }
        }
        checkSchoolManager();
        document.getElementById('saveGeneralSettingsBtn').onclick = function() {
            localStorage.setItem('schoolName', document.getElementById('schoolName').value);
            localStorage.setItem('managerName', document.getElementById('managerName').value);
            alert('تم حفظ اسم المدرسة والمدير!');
            checkSchoolManager();
        };
        // عند تحميل الصفحة، إذا لم يكن الاسم محفوظًا، أظهر النموذج
        window.addEventListener('DOMContentLoaded', checkSchoolManager);
        // تحديث قيمة مستوى صوت الطوارئ وتخزينها في قاعدة البيانات
        const emergencyVolumeInput = document.getElementById('emergencyVolume');
        const emergencyVolumeValue = document.getElementById('emergencyVolumeValue');
        async function setEmergencyVolume(val) {
            emergencyVolumeValue.textContent = parseFloat(val).toFixed(2);
            if (window.require) {
                const { ipcRenderer } = require('electron');
                await ipcRenderer.invoke('db-set', 'emergencyVolume', val);
            } else {
                localStorage.setItem('emergencyVolume', val);
            }
        }
        emergencyVolumeInput.oninput = function() {
            setEmergencyVolume(emergencyVolumeInput.value);
        };
        // تحميل القيمة المحفوظة عند فتح الإعدادات
        window.addEventListener('DOMContentLoaded', async function() {
            let saved = null;
            if (window.require) {
                const { ipcRenderer } = require('electron');
                saved = await ipcRenderer.invoke('db-get', 'emergencyVolume');
            } else {
                saved = localStorage.getItem('emergencyVolume');
            }
            if (saved !== null && saved !== undefined) {
                emergencyVolumeInput.value = saved;
                emergencyVolumeValue.textContent = parseFloat(saved).toFixed(2);
            }
        });
        // إدارة الحصص
        let classes = [];
        let alerts = [];
        async function loadClasses() {
            if (window.require) {
                const { ipcRenderer } = require('electron');
                classes = await ipcRenderer.invoke('db-get', 'classes') || [];
            } else {
                classes = JSON.parse(localStorage.getItem('classes') || '[]');
            }
        }
        async function loadAlerts() {
            if (window.require) {
                const { ipcRenderer } = require('electron');
                alerts = await ipcRenderer.invoke('db-get', 'alerts') || [];
            } else {
                alerts = JSON.parse(localStorage.getItem('alerts') || '[]');
            }
        }

        async function renderClasses() {
            await loadClasses();
            const list = document.getElementById('classesList');
            if (!list) return;
            list.innerHTML = classes.length === 0 ? '<p style="color:#fff8">لا توجد حصص مضافة</p>' :
                classes.map((c, i) => `<div style='background:#fff1;padding:8px;border-radius:8px;margin-bottom:6px;'>
                    <b>المادة:</b> <span>${c.subjectName}</span> <b>| الصف:</b> <span>${c.gradeName}</span> <b>| اسم الحصة:</b> <span>${c.className}</span><br>
                    <b>المعلم:</b> ${c.teacherName}<br>
                    <b>الأيام:</b> ${Array.isArray(c.days) ? c.days.join('، ') : c.days}<br>
                    <b>الوقت:</b> ${c.startTime} - ${c.endTime}<br>
                    <button onclick='deleteClass(${i})' style='float:left;background:#f44336;color:#fff;border:none;border-radius:6px;padding:2px 8px;cursor:pointer;'>حذف</button>
                </div>`).join('');
        }
        async function addClass() {
            const className = document.getElementById('className').value.trim();
            const subjectName = document.getElementById('subjectName').value.trim();
            const gradeName = document.getElementById('gradeName').value.trim();
            const teacherName = document.getElementById('teacherName').value.trim();
            const startTime = document.getElementById('startTime').value;
            const endTime = document.getElementById('endTime').value;
            const days = Array.from(document.querySelectorAll('input[name="days"]:checked')).map(cb => cb.value);
            if (!className || !subjectName || !gradeName || !teacherName || !startTime || !endTime || days.length === 0) {
                return alert('يرجى تعبئة جميع بيانات الحصة');
            }
            const newClass = { className, subjectName, gradeName, teacherName, days, startTime, endTime };
            if (window.require) {
                const { ipcRenderer } = require('electron');
                await ipcRenderer.invoke('db-push', 'classes', newClass);
            } else {
                classes.push(newClass);
                localStorage.setItem('classes', JSON.stringify(classes));
            }
            await renderClasses();
            document.getElementById('className').value = '';
            document.getElementById('subjectName').value = '';
            document.getElementById('gradeName').value = '';
            document.getElementById('teacherName').value = '';
            document.querySelectorAll('input[name="days"]').forEach(cb => cb.checked = false);
            document.getElementById('startTime').value = '';
            document.getElementById('endTime').value = '';
            document.getElementById('className').focus();
        }
        async function deleteClass(i) {
            if (window.require) {
                const { ipcRenderer } = require('electron');
                // حذف حسب الفهرس
                let current = await ipcRenderer.invoke('db-get', 'classes') || [];
                current.splice(i, 1);
                await ipcRenderer.invoke('db-set', 'classes', current);
            } else {
                classes.splice(i, 1);
                localStorage.setItem('classes', JSON.stringify(classes));
            }
            await renderClasses();
        }
        renderClasses();

        // إدارة التنبيهات
        async function renderAlerts() {
            await loadAlerts();
            const list = document.getElementById('alertsList');
            if (!list) return;
            list.innerHTML = alerts.length === 0 ? '<p style="color:#fff8">لا توجد تنبيهات مضافة</p>' :
                alerts.map((a, i) => `<div style='background:#fff1;padding:8px;border-radius:8px;margin-bottom:6px;'>
                    <b>${a.type === 'start' ? 'بداية' : a.type === 'end' ? 'نهاية' : a.type === 'assembly' ? 'الطابور الصباحي' : 'استراحة'}</b> - ${a.time} - ${a.subject} (${Array.isArray(a.days) ? a.days.join('، ') : a.days})
                    <button onclick='deleteAlert(${i})' style='float:left;background:#f44336;color:#fff;border:none;border-radius:6px;padding:2px 8px;cursor:pointer;'>حذف</button>
                </div>`).join('');
        }
        async function addAlert() {
            const type = document.getElementById('alertType').value;
            const time = document.getElementById('alertTime').value;
            const subject = document.getElementById('alertSubject').value;
            const days = Array.from(document.querySelectorAll('input[name="days"]:checked')).map(cb => cb.value);
            const soundInput = document.getElementById('alertSound');
            let sound = '';
            if (!soundInput.files || soundInput.files.length === 0) {
                alert('يرجى اختيار صوت التنبيه (إجباري)');
                return;
            }
            const file = soundInput.files[0];
            sound = URL.createObjectURL(file);
            if (!type || !time || !subject || days.length === 0) return alert('يرجى تعبئة جميع بيانات التنبيه');
            const newAlert = { type, time, subject, days, sound };
            if (window.require) {
                const { ipcRenderer } = require('electron');
                await ipcRenderer.invoke('db-push', 'alerts', newAlert);
            } else {
                alerts.push(newAlert);
                localStorage.setItem('alerts', JSON.stringify(alerts));
            }
            await renderAlerts();
        }
        async function deleteAlert(i) {
            if (window.require) {
                const { ipcRenderer } = require('electron');
                let current = await ipcRenderer.invoke('db-get', 'alerts') || [];
                current.splice(i, 1);
                await ipcRenderer.invoke('db-set', 'alerts', current);
            } else {
                alerts.splice(i, 1);
                localStorage.setItem('alerts', JSON.stringify(alerts));
            }
            await renderAlerts();
        }
        renderAlerts();

        // حفظ الإعدادات
        document.getElementById('settingsForm').onsubmit = function(e) {
            e.preventDefault();
            // حفظ الإعدادات العامة
            localStorage.setItem('schoolName', document.getElementById('schoolName').value);
            localStorage.setItem('managerName', document.getElementById('managerName').value);
            localStorage.setItem('bellVolume', document.getElementById('bellVolume').value);
            localStorage.setItem('themeColor', document.getElementById('themeColor').value);
            alert('✅ تم حفظ الإعدادات بنجاح!');
            window.close();
        };
    </script>
</body>
</html>
