<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>شاشة ترحيب - الجرس المدرسي</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        .splash-box {
            background: rgba(255,255,255,0.13);
            border-radius: 22px;
            box-shadow: 0 8px 32px rgba(76, 175, 80, 0.18);
            padding: 38px 32px 28px 32px;
            text-align: center;
            min-width: 320px;
            max-width: 90vw;
            border: 1.5px solid #fff3;
            position: relative;
        }
        .splash-title {
            font-size: 2.1rem;
            font-weight: bold;
            color: #fff;
            margin-bottom: 18px;
            letter-spacing: 1px;
            text-shadow: 0 2px 8px #0002;
        }
        .splash-logo {
            font-size: 3.2rem;
            margin-bottom: 10px;
            animation: bell 1.2s infinite alternate;
        }
        @keyframes bell {
            0% { transform: rotate(-10deg); }
            100% { transform: rotate(10deg); }
        }
        .splash-desc {
            font-size: 1.1rem;
            color: #f3f3f3;
            margin-bottom: 18px;
            opacity: 0.92;
        }
        .splash-footer {
            position: absolute;
            bottom: 10px;
            left: 0;
            width: 100%;
            font-size: 0.98rem;
            color: #fff;
            opacity: 0.93;
            text-align: center;
        }
        .splash-footer strong {
            color: #ffd700;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="splash-box">
        <div class="splash-logo">🔔</div>
        <div class="splash-title">نظام الجرس المدرسي الإلكتروني</div>
        <div class="splash-desc">برنامج احترافي لإدارة أوقات الحصص والتنبيهات<br>يدعم العربية ويعمل تلقائياً مع بدء تشغيل ويندوز</div>
        <div id="splash-countdown" class="splash-countdown">5</div>
        <div class="splash-footer">
            جميع الحقوق محفوظة &copy; <strong>محمد الشوامرة</strong> <br> 0566000140
        </div>
    </div>
    <script>
        // عداد تنازلي مرئي
        const countdownElem = document.getElementById('splash-countdown');
        let seconds = 5;
        countdownElem.textContent = seconds;
        countdownElem.style.fontSize = '2.5rem';
        countdownElem.style.fontWeight = 'bold';
        countdownElem.style.color = '#ffd700';
        countdownElem.style.margin = '18px auto 0 auto';
        countdownElem.style.width = '70px';
        countdownElem.style.height = '70px';
        countdownElem.style.display = 'flex';
        countdownElem.style.alignItems = 'center';
        countdownElem.style.justifyContent = 'center';
        countdownElem.style.background = 'rgba(255,255,255,0.18)';
        countdownElem.style.borderRadius = '50%';
        countdownElem.style.boxShadow = '0 2px 12px #0002';
        countdownElem.style.transition = 'all 0.3s';
        let interval = setInterval(() => {
            seconds--;
            if (seconds > 0) {
                countdownElem.textContent = seconds;
                countdownElem.style.transform = 'scale(1.1)';
                setTimeout(()=>countdownElem.style.transform = 'scale(1)', 150);
            } else {
                countdownElem.textContent = 'ابدأ!';
                clearInterval(interval);
            }
        }, 1000);
    </script>
</body>
</html>
