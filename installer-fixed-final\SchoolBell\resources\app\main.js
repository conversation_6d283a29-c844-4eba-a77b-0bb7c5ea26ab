const { app, BrowserWind<PERSON>, <PERSON>u, Tray, nativeImage, ipcMain } = require('electron');
const fs = require('fs');
let tray = null;
const path = require('path');

// استخدام lowdb بطريقة متوافقة
let Low, JSONFile;
async function initLowDB() {
    try {
        const lowdb = await import('lowdb');
        Low = lowdb.Low;
        JSONFile = lowdb.JSONFile;
    } catch (error) {
        console.error('Error importing lowdb:', error);
        // fallback إلى استخدام fs مباشرة
        Low = null;
        JSONFile = null;
    }
}


// تفعيل البدء مع التشغيل
app.setLoginItemSettings({
    openAtLogin: true,
    path: process.execPath,
    args: ['--hidden']
});

// إعداد قاعدة بيانات lowdb
let db;
let dbPath;

async function initDatabase() {
    try {
        // تهيئة lowdb أولاً
        await initLowDB();

        dbPath = path.join(app.getPath('userData'), 'school-bell-db.json');
        console.log('Database path:', dbPath);

        // إنشاء المجلد إذا لم يكن موجوداً
        const dbDir = path.dirname(dbPath);
        if (!fs.existsSync(dbDir)) {
            fs.mkdirSync(dbDir, { recursive: true });
        }

        // إنشاء ملف قاعدة البيانات إذا لم يكن موجوداً
        if (!fs.existsSync(dbPath)) {
            const defaultData = { schoolName: '', managerName: '', classes: [], alerts: [] };
            fs.writeFileSync(dbPath, JSON.stringify(defaultData, null, 2), 'utf8');
            console.log('Created default database file');
        }

        if (Low && JSONFile) {
            const adapter = new JSONFile(dbPath);
            db = new Low(adapter);
            await loadDb();
            console.log('Database initialized successfully with lowdb');
        } else {
            // fallback إلى استخدام fs مباشرة
            console.log('Using fallback file system database');
            db = {
                data: JSON.parse(fs.readFileSync(dbPath, 'utf8')),
                read: async function() {
                    this.data = JSON.parse(fs.readFileSync(dbPath, 'utf8'));
                },
                write: async function() {
                    fs.writeFileSync(dbPath, JSON.stringify(this.data, null, 2), 'utf8');
                }
            };
        }
        console.log('Database initialized successfully');
    } catch (error) {
        console.error('Database initialization error:', error);
        // إنشاء قاعدة بيانات افتراضية في الذاكرة
        db = {
            data: { schoolName: '', managerName: '', classes: [], alerts: [] },
            read: async function() {},
            write: async function() {}
        };
    }
}

async function loadDb() {
    try {
        if (db && db.read) {
            await db.read();
            if (!db.data) {
                db.data = { schoolName: '', managerName: '', classes: [], alerts: [] };
            }
        }
    } catch (error) {
        console.error('Error loading database:', error);
        if (db) {
            db.data = { schoolName: '', managerName: '', classes: [], alerts: [] };
        }
    }
}

async function saveDb() {
    try {
        if (db && db.write) {
            await db.write();
        }
    } catch (error) {
        console.error('Error saving database:', error);
    }
}

let mainWindow;

async function createWindow() {
    try {
        console.log('Creating main window...');
        // إنشاء النافذة الرئيسية
        mainWindow = new BrowserWindow({
        width: 1000,
        height: 700,
        minWidth: 800,
        minHeight: 600,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js')
        },
        icon: path.join(__dirname, 'assets', 'icon.ico'),
        frame: false, // إطار حديث بدون شريط النظام التقليدي
        titleBarStyle: 'hidden',
        show: true, // إظهار النافذة فوراً
        center: true,
        resizable: true,
        maximizable: true,
        minimizable: true,
        closable: true,
        alwaysOnTop: false,
        focusable: true,
        title: 'نظام الجرس المدرسي الإلكتروني'
    });



    // إلغاء شريط القوائم العلوي (File, Edit ...)
    Menu.setApplicationMenu(null);

    // إضافة أيقونة البرنامج في شريط المهام وبجوار الساعة
    if (!tray) {
        const trayIcon = nativeImage.createFromPath(path.join(__dirname, 'assets', 'icon.ico'));
        tray = new Tray(trayIcon);
        tray.setToolTip('نظام الجرس المدرسي الإلكتروني');
        tray.on('click', () => {
            if (mainWindow) {
                mainWindow.show();
                mainWindow.focus();
            }
        });
    }


    // شاشة ترحيب (splash)
    let splash = new BrowserWindow({
        width: 420,
        height: 320,
        frame: false,
        alwaysOnTop: true,
        transparent: true,
        resizable: false,
        center: true,
        show: true
    });
    console.log('Loading splash.html...');
    splash.loadFile('splash.html').then(() => {
        console.log('Splash loaded successfully');
    }).catch((err) => {
        console.error('Error loading splash.html:', err);
    });


    // تحميل الصفحة الرئيسية بعد 5 ثوانٍ
    setTimeout(() => {
        splash.close();
        console.log('Loading renderer.html...');
        mainWindow.loadFile('renderer.html').then(() => {
            console.log('renderer.html loaded successfully');
        }).catch((err) => {
            console.error('Error loading renderer.html:', err);
        });
    }, 5000);

    // إظهار النافذة عندما تكون جاهزة
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        mainWindow.focus();
        mainWindow.moveTop();

        // ضمان ظهور النافذة
        setTimeout(() => {
            if (mainWindow && !mainWindow.isDestroyed()) {
                mainWindow.show();
                mainWindow.focus();
                mainWindow.setAlwaysOnTop(true);
                setTimeout(() => {
                    mainWindow.setAlwaysOnTop(false);
                }, 2000);
            }
        }, 1000);
    });

    // التعامل مع إغلاق النافذة
    mainWindow.on('closed', () => {
        mainWindow = null;
    });

    // فتح أدوات المطور في وضع التطوير (اختياري)
    // mainWindow.webContents.openDevTools();

    } catch (error) {
        console.error('Error creating window:', error);
        // إنشاء نافذة بسيطة في حالة الخطأ
        mainWindow = new BrowserWindow({
            width: 800,
            height: 600,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: path.join(__dirname, 'preload.js')
            }
        });
        mainWindow.loadFile('renderer.html');
    }
}

// هذا الحدث سيتم استدعاؤه عندما يكون Electron جاهزاً
// لإنشاء نوافذ المتصفح.
app.whenReady().then(async () => {
    try {
        console.log('App is ready, initializing...');
        await initDatabase();
        await createWindow();
        console.log('App initialization completed');
    } catch (error) {
        console.error('App initialization error:', error);
    }
    // دعم أزرار الإطار الحديث
    ipcMain.on('window-minimize', () => {
        if (mainWindow) mainWindow.minimize();
    });
    ipcMain.on('window-maximize', () => {
        if (mainWindow) {
            if (mainWindow.isMaximized()) mainWindow.unmaximize();
            else mainWindow.maximize();
        }
    });
    ipcMain.on('window-close', () => {
        if (mainWindow) mainWindow.close();
    });

    // قنوات IPC لقراءة/كتابة البيانات من/إلى قاعدة البيانات
    ipcMain.handle('db-get', async (event, key) => {
        await loadDb();
        return db.data[key];
    });
    ipcMain.handle('db-set', async (event, key, value) => {
        await loadDb();
        db.data[key] = value;
        await saveDb();
        return true;
    });
});

// الخروج عندما يتم إغلاق جميع النوافذ
app.on('window-all-closed', () => {
    // على macOS من الشائع أن تبقى التطبيقات ونافذة القائمة
    // نشطة حتى يقوم المستخدم بالخروج صراحة باستخدام Cmd + Q
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    // على macOS من الشائع إعادة إنشاء نافذة في التطبيق عندما
    // يتم النقر على أيقونة dock ولا توجد نوافذ أخرى مفتوحة.
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// في هذا الملف يمكنك تضمين باقي كود العملية الرئيسية الخاص بتطبيقك.
// يمكنك أيضاً وضعها في ملفات منفصلة وتطلبها هنا.
