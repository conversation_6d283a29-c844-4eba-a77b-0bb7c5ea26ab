const { app, BrowserWindow, <PERSON><PERSON>, Tray, nativeImage } = require('electron');
const { Low, JSONFile } = require('lowdb');
const fs = require('fs');
let tray = null;
const path = require('path');


// تفعيل البدء مع التشغيل
app.setLoginItemSettings({
    openAtLogin: true,
    path: process.execPath,
    args: ['--hidden']
});

// إعداد قاعدة بيانات lowdb
const dbPath = path.join(app.getPath('userData'), 'school-bell-db.json');
if (!fs.existsSync(dbPath)) {
    fs.writeFileSync(dbPath, JSON.stringify({ schoolName: '', managerName: '', classes: [], alerts: [] }, null, 2));
}
const adapter = new JSONFile(dbPath);
const db = new Low(adapter);
async function loadDb() { await db.read(); if (!db.data) db.data = { schoolName: '', managerName: '', classes: [], alerts: [] }; }
async function saveDb() { await db.write(); }

let mainWindow;

async function createWindow() {
    // إنشاء النافذة الرئيسية
    mainWindow = new BrowserWindow({
        width: 800,
        height: 600,
        minWidth: 600,
        minHeight: 400,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js')
        },
        icon: path.join(__dirname, 'assets', 'icon.ico'),
        frame: false, // إطار حديث بدون شريط النظام التقليدي
        titleBarStyle: 'hidden',
        show: false,
        center: true,
        resizable: true,
        maximizable: true,
        minimizable: true,
        closable: true,
        title: 'نظام الجرس المدرسي الإلكتروني - مصغر'
    });



    // إلغاء شريط القوائم العلوي (File, Edit ...)
    Menu.setApplicationMenu(null);

    // إضافة أيقونة البرنامج في شريط المهام وبجوار الساعة
    if (!tray) {
        const trayIcon = nativeImage.createFromPath(path.join(__dirname, 'assets', 'icon.ico'));
        tray = new Tray(trayIcon);
        tray.setToolTip('نظام الجرس المدرسي الإلكتروني');
        tray.on('click', () => {
            if (mainWindow) {
                mainWindow.show();
                mainWindow.focus();
            }
        });
    }


    // شاشة ترحيب (splash)
    let splash = new BrowserWindow({
        width: 420,
        height: 320,
        frame: false,
        alwaysOnTop: true,
        transparent: true,
        resizable: false,
        center: true,
        show: true
    });
    console.log('Loading splash.html...');
    splash.loadFile('splash.html').then(() => {
        console.log('Splash loaded successfully');
    }).catch((err) => {
        console.error('Error loading splash.html:', err);
    });


    // تحميل الصفحة الرئيسية بعد 5 ثوانٍ
    setTimeout(() => {
        splash.close();
        console.log('Loading renderer.html...');
        mainWindow.loadFile('renderer.html').then(() => {
            console.log('renderer.html loaded successfully');
        }).catch((err) => {
            console.error('Error loading renderer.html:', err);
        });
    }, 5000);

    // إظهار النافذة عندما تكون جاهزة
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        // تركيز النافذة
        if (mainWindow) {
            mainWindow.focus();
        }
    });

    // التعامل مع إغلاق النافذة
    mainWindow.on('closed', () => {
        mainWindow = null;
    });

    // فتح أدوات المطور في وضع التطوير (اختياري)
    // mainWindow.webContents.openDevTools();
}

// هذا الحدث سيتم استدعاؤه عندما يكون Electron جاهزاً
// لإنشاء نوافذ المتصفح.
const { ipcMain } = require('electron');
app.whenReady().then(async () => {
    await loadDb();
    createWindow();
    // دعم أزرار الإطار الحديث
    ipcMain.on('window-minimize', () => {
        if (mainWindow) mainWindow.minimize();
    });
    ipcMain.on('window-maximize', () => {
        if (mainWindow) {
            if (mainWindow.isMaximized()) mainWindow.unmaximize();
            else mainWindow.maximize();
        }
    });
    ipcMain.on('window-close', () => {
        if (mainWindow) mainWindow.close();
    });

    // قنوات IPC لقراءة/كتابة البيانات من/إلى قاعدة البيانات
    ipcMain.handle('db-get', async (event, key) => {
        await loadDb();
        return db.data[key];
    });
    ipcMain.handle('db-set', async (event, key, value) => {
        await loadDb();
        db.data[key] = value;
        await saveDb();
        return true;
    });
});

// الخروج عندما يتم إغلاق جميع النوافذ
app.on('window-all-closed', () => {
    // على macOS من الشائع أن تبقى التطبيقات ونافذة القائمة
    // نشطة حتى يقوم المستخدم بالخروج صراحة باستخدام Cmd + Q
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    // على macOS من الشائع إعادة إنشاء نافذة في التطبيق عندما
    // يتم النقر على أيقونة dock ولا توجد نوافذ أخرى مفتوحة.
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// في هذا الملف يمكنك تضمين باقي كود العملية الرئيسية الخاص بتطبيقك.
// يمكنك أيضاً وضعها في ملفات منفصلة وتطلبها هنا.
